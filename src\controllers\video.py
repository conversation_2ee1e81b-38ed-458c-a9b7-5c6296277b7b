from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
import stripe
import json
import os
import boto3
from src.models.db import db
from src.models.content import Video, Template, VideoUpload, Upload
from src.utils.runway import generate_video_with_runway
from src.utils.local_storage import get_file_url
from src import limiter

video_bp = Blueprint('video', __name__)

@video_bp.route('/templates')
@login_required
def templates():
    """Display available video templates"""
    # Check if user has uploaded at least 10 photos
    user_uploads = Upload.query.filter_by(user_id=current_user.id).all()
    image_uploads = [u for u in user_uploads if u.file_type == 'image']

    if len(image_uploads) < 10:
        flash(f'You need to upload at least 10 photos to create a video. You currently have {len(image_uploads)} photos uploaded.', 'warning')
        return redirect(url_for('upload.upload_files'))

    templates = Template.query.filter_by(is_active=True).all()
    return render_template('video/templates_new.html', title='Create Your Memorial Video', templates=templates, photo_count=len(image_uploads))

@video_bp.route('/create')
@login_required
def create():
    """Display the create video page"""
    template_id = request.args.get('template_id')

    if not template_id:
        flash('Please select a template first.', 'warning')
        return redirect(url_for('video.templates'))

    # Verify template exists
    template = Template.query.get(template_id)
    if not template:
        flash('Invalid template selected.', 'danger')
        return redirect(url_for('video.templates'))

    # Get price from config
    price = current_app.config['STRIPE_PRICE']

    return render_template('video/create.html', title='Create Your Memorial Video', template=template, price=price)

@video_bp.route('/create-video', methods=['POST'])
@login_required
@limiter.limit("5 per hour")  # Limit video creation
def create_video():
    """Create a new video project"""
    template_id = request.form.get('template_id')
    title = request.form.get('title')
    description = request.form.get('description', '')
    upload_ids_json = request.form.get('upload_ids')
    customization_json = request.form.get('customization')

    try:
        upload_ids = json.loads(upload_ids_json) if upload_ids_json else []
        customization = json.loads(customization_json) if customization_json else {}
    except json.JSONDecodeError:
        flash('Invalid data format', 'danger')
        return redirect(url_for('video.templates'))

    if not template_id or not title or not upload_ids:
        flash('Missing required information', 'danger')
        return redirect(url_for('video.templates'))

    # Verify template exists
    template = Template.query.get_or_404(template_id)

    # Verify uploads exist and belong to user
    uploads = Upload.query.filter(
        Upload.id.in_(upload_ids),
        Upload.user_id == current_user.id
    ).all()

    if len(uploads) != len(upload_ids):
        flash('One or more selected files are invalid', 'danger')
        return redirect(url_for('video.templates'))

    # Create video record
    video = Video(
        user_id=current_user.id,
        template_id=template.id,
        title=title,
        description=description,
        status='pending',
        customization=json.dumps(customization) if customization else None
    )
    db.session.add(video)
    db.session.flush()  # Get video ID without committing

    # Associate uploads with video
    for upload_id in upload_ids:
        video_upload = VideoUpload(
            video_id=video.id,
            upload_id=upload_id
        )
        db.session.add(video_upload)

    db.session.commit()

    # Redirect to payment
    return redirect(url_for('video.payment', video_id=video.id))

@video_bp.route('/payment/<int:video_id>')
@login_required
def payment(video_id):
    """Display payment page for a video"""
    video = Video.query.filter_by(id=video_id, user_id=current_user.id).first_or_404()

    # If already paid, redirect to dashboard
    if video.payment_id:
        flash('This video has already been paid for', 'info')
        return redirect(url_for('main.dashboard'))

    # Set up Stripe
    stripe.api_key = current_app.config['STRIPE_SECRET_KEY']

    # Create a payment session
    session = stripe.checkout.Session.create(
        payment_method_types=['card'],
        line_items=[{
            'price_data': {
                'currency': 'usd',
                'product_data': {
                    'name': f'FurEverMemories Video: {video.title}',
                    'description': 'AI-generated pet memorial video'
                },
                'unit_amount': current_app.config['STRIPE_PRICE']
            },
            'quantity': 1
        }],
        mode='payment',
        success_url=url_for('video.payment_success', video_id=video.id, _external=True) + '?session_id={CHECKOUT_SESSION_ID}',
        cancel_url=url_for('video.payment_cancel', video_id=video.id, _external=True)
    )

    return render_template(
        'video/payment.html',
        title='Payment',
        video=video,
        checkout_session_id=session.id,
        stripe_publishable_key=current_app.config['STRIPE_PUBLISHABLE_KEY']
    )

@video_bp.route('/payment/success/<int:video_id>')
@login_required
@limiter.limit("10 per hour")  # Prevent payment abuse
def payment_success(video_id):
    """Handle successful payment"""
    video = Video.query.filter_by(id=video_id, user_id=current_user.id).first_or_404()
    session_id = request.args.get('session_id')

    if not session_id:
        flash('Invalid payment session', 'danger')
        return redirect(url_for('main.dashboard'))

    # Verify payment with Stripe
    stripe.api_key = current_app.config['STRIPE_SECRET_KEY']
    try:
        session = stripe.checkout.Session.retrieve(session_id)

        # Update video record with payment info
        video.payment_id = session.payment_intent
        video.status = 'processing'
        db.session.commit()

        # Trigger video generation (in a real app, this would be a background task)
        # For now, we'll just simulate it
        try:
            generate_video(video.id)
        except Exception as e:
            current_app.logger.error(f"Error generating video: {str(e)}")

        flash('Payment successful! Your video is now being generated.', 'success')
        return redirect(url_for('main.dashboard'))

    except Exception as e:
        flash(f'Error verifying payment: {str(e)}', 'danger')
        return redirect(url_for('main.dashboard'))

@video_bp.route('/payment/cancel/<int:video_id>')
@login_required
def payment_cancel(video_id):
    """Handle cancelled payment"""
    flash('Payment was cancelled. Your video has not been generated.', 'warning')
    return redirect(url_for('main.dashboard'))

@video_bp.route('/view/<token>')
def view_video(token):
    """View a generated video using a secure token"""
    video = Video.query.filter_by(view_token=token).first_or_404()

    # Check if video is completed
    if video.status != 'completed':
        flash('This video is not yet available for viewing', 'warning')
        if current_user.is_authenticated and video.user_id == current_user.id:
            return redirect(url_for('main.dashboard'))
        return redirect(url_for('main.index'))

    # Generate a presigned URL for the S3 object
    s3_client = boto3.client(
        's3',
        aws_access_key_id=current_app.config['AWS_ACCESS_KEY'],
        aws_secret_access_key=current_app.config['AWS_SECRET_KEY'],
        region_name=current_app.config['AWS_REGION']
    )

    video_url = s3_client.generate_presigned_url(
        'get_object',
        Params={
            'Bucket': current_app.config['AWS_BUCKET_NAME'],
            'Key': video.s3_key
        },
        ExpiresIn=3600  # URL expires in 1 hour
    )

    return render_template(
        'video/view.html',
        title=f'Video: {video.title}',
        video=video,
        video_url=video_url
    )

def generate_video(video_id):
    """
    Generate a video using RunwayML API
    This would typically be a background task
    """
    video = Video.query.get(video_id)
    if not video or video.status != 'processing':
        current_app.logger.error(f"Cannot generate video {video_id}: Invalid video or status")
        return False

    # Call the RunwayML API to generate the video
    return generate_video_with_runway(video_id)

# Webhook callback routes removed - RunwayML gen4_turbo uses polling instead of webhooks

@video_bp.route('/api/videos', methods=['GET'])
@login_required
@limiter.limit("100 per hour")  # API rate limiting
def get_videos():
    """API endpoint to get user's videos"""
    videos = Video.query.filter_by(user_id=current_user.id).order_by(Video.created_at.desc()).all()

    result = []
    for video in videos:
        video_data = {
            'id': video.id,
            'title': video.title,
            'description': video.description,
            'status': video.status,
            'created_at': video.created_at.isoformat(),
            'completed_at': video.completed_at.isoformat() if video.completed_at else None,
            'view_token': video.view_token
        }

        # Add video URL if the video is completed
        if video.status == 'completed' and video.s3_key:
            video_data['url'] = get_file_url(video.s3_key)

        result.append(video_data)

    return jsonify(result)

@video_bp.route('/api/preview', methods=['POST'])
@login_required
@limiter.limit("10 per hour")  # Limit preview generation
def generate_preview():
    """API endpoint to generate a preview video"""
    if not request.is_json:
        return jsonify({'success': False, 'error': 'Invalid request format'}), 400

    data = request.get_json()
    template_id = data.get('template_id')
    upload_ids = data.get('upload_ids', [])
    customization = data.get('customization')

    if not template_id or not upload_ids:
        return jsonify({'success': False, 'error': 'Missing required parameters'}), 400

    # Verify template exists
    template = Template.query.get(template_id)
    if not template:
        return jsonify({'success': False, 'error': 'Template not found'}), 404

    # Verify uploads exist and belong to user
    uploads = Upload.query.filter(
        Upload.id.in_(upload_ids),
        Upload.user_id == current_user.id
    ).all()

    if len(uploads) != len(upload_ids):
        return jsonify({'success': False, 'error': 'One or more selected files are invalid'}), 400

    # Generate preview
    from src.utils.runway import generate_preview_video
    result = generate_preview_video(template_id, upload_ids, customization)

    return jsonify(result)

@video_bp.route('/api/videos/<int:video_id>', methods=['DELETE'])
@login_required
@limiter.limit("20 per hour")  # Prevent delete abuse
def delete_video(video_id):
    """API endpoint to delete a video (only pending videos can be deleted)"""
    current_app.logger.info(f"Delete video request for video_id: {video_id}, user_id: {current_user.id}")

    video = Video.query.filter_by(id=video_id, user_id=current_user.id).first()
    if not video:
        current_app.logger.warning(f"Video {video_id} not found for user {current_user.id}")
        return jsonify({'success': False, 'error': 'Video not found'}), 404

    current_app.logger.info(f"Found video: {video.title}, status: {video.status}")

    # Only allow deletion of pending videos (not paid/processing/completed)
    if video.status != 'pending':
        current_app.logger.warning(f"Cannot delete video {video_id} with status {video.status}")
        return jsonify({'success': False, 'error': 'Only pending videos can be deleted'}), 400

    try:
        # Delete associated video uploads first
        video_uploads = VideoUpload.query.filter_by(video_id=video.id).all()
        current_app.logger.info(f"Found {len(video_uploads)} video uploads to delete")

        for video_upload in video_uploads:
            db.session.delete(video_upload)

        # Delete the video from database
        db.session.delete(video)
        db.session.commit()

        current_app.logger.info(f"Successfully deleted video {video_id}")
        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting video {video_id}: {str(e)}")
        import traceback
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': f'Failed to delete video: {str(e)}'}), 500

@video_bp.route('/api/preview/<preview_id>', methods=['GET'])
@login_required
@limiter.limit("50 per hour")  # API rate limiting
def get_preview(preview_id):
    """API endpoint to get preview status and URL"""
    # Validate preview_id format (should be UUID)
    import re
    if not re.match(r'^[a-f0-9-]{36}$', preview_id):
        return jsonify({'success': False, 'error': 'Invalid preview ID format'}), 400

    preview_path = f"previews/{preview_id}.json"
    file_path = os.path.join(current_app.root_path, 'static', preview_path)

    if not os.path.exists(file_path):
        return jsonify({'success': False, 'error': 'Preview not found'}), 404

    try:
        with open(file_path, 'r') as f:
            preview_data = json.load(f)

        # Add video URL if available
        if preview_data.get('status') == 'completed' and preview_data.get('video_path'):
            preview_data['video_url'] = url_for('static', filename=preview_data['video_path'])

        return jsonify(preview_data)
    except Exception as e:
        current_app.logger.error(f"Error reading preview data: {str(e)}")
        return jsonify({'success': False, 'error': 'Error reading preview data'}), 500

# Preview callback route removed - using polling instead of webhooks
