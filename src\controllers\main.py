from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from src.forms.main_forms import ProfileForm
from src.models.db import db
from src.models.content import Upload, Video

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Homepage route"""
    return render_template('main/index.html', title='FurEverMemories - Create a Lasting Tribute to Your Pet')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """User dashboard route"""
    from src.models.content import Upload

    # Get user's photo count
    user_uploads = Upload.query.filter_by(user_id=current_user.id).all()
    image_uploads = [u for u in user_uploads if u.file_type == 'image']
    photo_count = len(image_uploads)

    return render_template('main/dashboard.html', title='Dashboard', photo_count=photo_count)

@main_bp.route('/contact')
def contact():
    """Contact page route"""
    return render_template('main/contact.html', title='Contact Us')

@main_bp.route('/about')
def about():
    """About page route"""
    return render_template('main/about.html', title='About FurEverMemories')

@main_bp.route('/privacy')
def privacy():
    """Privacy policy route"""
    return render_template('main/privacy.html', title='Privacy Policy')

@main_bp.route('/terms')
def terms():
    """Terms of service route"""
    return render_template('main/terms.html', title='Terms of Service')

@main_bp.route('/faq')
def faq():
    """FAQ page route"""
    return render_template('main/faq.html', title='Frequently Asked Questions')

@main_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile page"""
    form = ProfileForm(obj=current_user)

    if form.validate_on_submit():
        current_user.first_name = form.first_name.data
        current_user.last_name = form.last_name.data
        current_user.email = form.email.data
        db.session.commit()
        flash('Your profile has been updated!', 'success')
        return redirect(url_for('main.profile'))

    # Get user's uploads and videos
    uploads = Upload.query.filter_by(user_id=current_user.id).all()
    videos = Video.query.filter_by(user_id=current_user.id).all()

    # Create recent activity list
    recent_activity = []

    # Add recent uploads
    for upload in sorted(uploads, key=lambda x: x.uploaded_at, reverse=True)[:3]:
        recent_activity.append({
            'date': upload.uploaded_at,
            'description': f'Uploaded file: {upload.original_file_name}'
        })

    # Add recent videos
    for video in sorted(videos, key=lambda x: x.created_at, reverse=True)[:3]:
        recent_activity.append({
            'date': video.created_at,
            'description': f'Created video: {video.title}'
        })

    # Sort by date
    recent_activity = sorted(recent_activity, key=lambda x: x['date'], reverse=True)[:5]

    return render_template('main/profile.html',
                          title='Your Profile',
                          form=form,
                          uploads=uploads,
                          videos=videos,
                          recent_activity=recent_activity)
