"""
RunwayML API integration for video generation using gen4_turbo model.
Based on comprehensive guide for image-to-video generation.
"""
import requests
import logging
import boto3
import time
from datetime import datetime, timezone
from flask import current_app
from runwayml import RunwayML
from src.models.db import db
from src.models.content import Video, VideoUpload, Template
from src.models.user import User
from src.utils.email import send_video_ready_notification, send_video_generation_failed_notification

logger = logging.getLogger(__name__)

# Template style prompts for gen4_turbo model
STYLE_PROMPTS = {
    "Heartfelt Tribute": "The pet is sitting calmly, looking into the distance, with a soft breeze moving its fur, in a dreamy, warm-toned style with soft focus and ethereal glow.",
    "Playful Memories": "The pet is running and jumping energetically in a bright, colorful park, with dynamic camera movements and vibrant colors.",
    "Serene Reflection": "The pet is resting peacefully by a tranquil lake, with smooth camera pans and calming reflections on the water.",
    "Timeless Journey": "The pet is walking through an old, historic town, with film-like grain and sepia tones, reminiscent of old memories.",
    "Nature's Embrace": "The pet is exploring a lush forest, with organic camera movements, blending into the natural environment."
}

def get_public_s3_url(upload):
    """
    Generate public S3 URL for an upload to be used in the RunwayML API.
    RunwayML requires publicly accessible HTTPS URLs.

    Args:
        upload: Upload object

    Returns:
        str: Public S3 URL
    """
    bucket_name = current_app.config['AWS_BUCKET_NAME']
    return f"https://{bucket_name}.s3.amazonaws.com/{upload.s3_key}"

def upload_to_s3(file_content, bucket, key):
    """
    Upload file content to S3 and return public URL.

    Args:
        file_content: File content to upload
        bucket: S3 bucket name
        key: S3 key for the file

    Returns:
        str: Public S3 URL
    """
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=current_app.config['AWS_ACCESS_KEY'],
            aws_secret_access_key=current_app.config['AWS_SECRET_KEY'],
            region_name=current_app.config['AWS_REGION']
        )

        s3_client.put_object(
            Body=file_content,
            Bucket=bucket,
            Key=key,
            ACL='public-read',
            ContentType='video/mp4'
        )
        return f"https://{bucket}.s3.amazonaws.com/{key}"
    except Exception as e:
        logger.error(f"S3 upload failed: {e}")
        raise Exception(f"S3 upload failed: {e}")

def download_and_store_video(video_url, bucket, key):
    """
    Download video from URL and store in S3.

    Args:
        video_url: URL of the video to download
        bucket: S3 bucket name
        key: S3 key for storing the video

    Returns:
        str: Public S3 URL of stored video
    """
    try:
        response = requests.get(video_url, stream=True)
        if response.status_code == 200:
            return upload_to_s3(response.content, bucket, key)
        else:
            raise Exception(f"Failed to download video: HTTP {response.status_code}")
    except Exception as e:
        logger.error(f"Error downloading and storing video: {e}")
        raise

def generate_memorial_video(image_url, style, duration=5, ratio="1280:720"):
    """
    Generate a memorial video using RunwayML gen4_turbo model.

    Args:
        image_url: Public HTTPS URL of the input image
        style: Template style name
        duration: Video duration in seconds (5 or 10)
        ratio: Video aspect ratio

    Returns:
        str: Video URL from RunwayML API
    """
    client = RunwayML(api_key=current_app.config['RUNWAYML_API_KEY'])

    # Get the appropriate prompt for the style
    prompt_text = STYLE_PROMPTS.get(style, "The pet is moving gently in a neutral setting.")

    try:
        # Create the image-to-video task
        task = client.image_to_video.create(
            model="gen4_turbo",
            prompt_image=image_url,
            prompt_text=prompt_text,
            ratio=ratio,
            duration=duration
        )

        task_id = task.id
        logger.info(f"RunwayML task created: {task_id}")

        # Poll for completion (no webhooks supported)
        while True:
            task_status = client.tasks.get(task_id)

            if task_status.status == "SUCCEEDED":
                video_url = task_status.output[0]
                logger.info(f"Video generation succeeded: {video_url}")
                return video_url
            elif task_status.status == "FAILED":
                error_msg = getattr(task_status, 'error', 'Unknown error')
                logger.error(f"Video generation failed: {error_msg}")
                raise Exception(f"Video generation failed: {error_msg}")
            else:
                # Still processing, wait 10 seconds as recommended
                logger.info(f"Task {task_id} status: {task_status.status}")
                time.sleep(10)

    except Exception as e:
        logger.error(f"RunwayML API error: {e}")
        raise Exception(f"Video generation error: {e}")

def generate_video_with_runway(video_id):
    """
    Generate a video using RunwayML gen4_turbo model.
    Updated to use image-to-video generation with polling.

    Args:
        video_id: ID of the video to generate

    Returns:
        bool: True if successful, False otherwise
    """
    video = Video.query.get(video_id)
    if not video or video.status != 'processing':
        logger.error(f"Cannot generate video {video_id}: Invalid video or status")
        return False

    try:
        # Get the uploads associated with this video
        video_uploads = VideoUpload.query.filter_by(video_id=video.id).all()
        uploads = [vu.upload for vu in video_uploads]

        if not uploads:
            logger.error(f"No uploads found for video {video_id}")
            video.status = 'failed'
            db.session.commit()
            return False

        # Get the template
        template = video.template
        if not template:
            logger.error(f"No template found for video {video_id}")
            video.status = 'failed'
            db.session.commit()
            return False

        # Use the first image upload for generation
        # Filter for image uploads only (gen4_turbo only accepts images)
        image_uploads = [u for u in uploads if u.file_type == 'image']

        if not image_uploads:
            logger.error(f"No image uploads found for video {video_id}")
            video.status = 'failed'
            db.session.commit()
            return False

        # Get public S3 URL for the first image
        primary_image = image_uploads[0]
        image_url = get_public_s3_url(primary_image)

        # Generate video using RunwayML
        video_url = generate_memorial_video(
            image_url=image_url,
            style=template.name,
            duration=5,  # 5 seconds for cost efficiency
            ratio="1280:720"
        )

        # Download and store the video in S3
        bucket_name = current_app.config['AWS_BUCKET_NAME']
        s3_key = f"videos/{video.user_id}/{video.id}/generated_video.mp4"

        stored_video_url = download_and_store_video(video_url, bucket_name, s3_key)

        # Update video record
        video.s3_key = s3_key
        video.status = 'completed'
        video.completed_at = datetime.now(timezone.utc)
        db.session.commit()

        # Send notification email
        user = User.query.get(video.user_id)
        send_video_ready_notification(user, video)

        logger.info(f"Video {video_id} generated successfully")
        return True

    except Exception as e:
        logger.exception(f"Error generating video: {str(e)}")
        video.status = 'failed'
        db.session.commit()

        # Send failure notification
        user = User.query.get(video.user_id)
        send_video_generation_failed_notification(user, video)
        return False

def generate_preview_video(template_id, upload_ids, customization=None):
    """
    Generate a short preview video using RunwayML gen4_turbo model.
    Uses polling instead of webhooks.

    Args:
        template_id: ID of the template to use
        upload_ids: List of upload IDs to include in the preview
        customization: Optional dictionary of customization parameters

    Returns:
        dict: Preview information including URL and status
    """
    try:
        # Get the template
        template = Template.query.get(template_id)
        if not template:
            logger.error(f"Cannot generate preview: Template {template_id} not found")
            return {"success": False, "error": "Template not found"}

        # Get the uploads
        from src.models.content import Upload
        uploads = Upload.query.filter(Upload.id.in_(upload_ids)).all()

        if len(uploads) == 0:
            logger.error("Cannot generate preview: No uploads found")
            return {"success": False, "error": "No uploads found"}

        # Filter for image uploads only (gen4_turbo only accepts images)
        image_uploads = [u for u in uploads if u.file_type == 'image']

        if not image_uploads:
            logger.error("Cannot generate preview: No image uploads found")
            return {"success": False, "error": "No image uploads found"}

        # Use the first image for preview
        primary_image = image_uploads[0]
        image_url = get_public_s3_url(primary_image)

        # Generate a short preview video (5 seconds)
        try:
            video_url = generate_memorial_video(
                image_url=image_url,
                style=template.name,
                duration=5,
                ratio="1280:720"
            )

            return {
                "success": True,
                "video_url": video_url,
                "template_id": template_id,
                "status": "completed"
            }

        except Exception as e:
            logger.error(f"Preview generation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    except Exception as e:
        logger.exception(f"Error generating preview: {str(e)}")
        return {"success": False, "error": str(e)}

# Note: Webhook functions removed as RunwayML gen4_turbo uses polling instead of webhooks
# The generate_video_with_runway function now handles the complete process including polling
