{% extends "base.html" %}

{% block title %}Upload Files - FurEverMemories{% endblock %}

{% block extra_css %}
<style>
    .upload-preview-container {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 20px;
    }

    .upload-preview-item {
        position: relative;
        width: 150px;
        height: 150px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .upload-preview-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }



    .upload-preview-item .remove-btn {
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: rgba(220, 53, 69, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        line-height: 1;
        opacity: 0;
        transition: opacity 0.3s ease, transform 0.2s ease;
        z-index: 10;
    }

    .upload-preview-item:hover .remove-btn {
        opacity: 1;
    }

    .upload-preview-item .remove-btn:hover {
        background-color: rgba(220, 53, 69, 1);
        transform: scale(1.1);
    }

    .progress {
        height: 10px;
        margin-top: 10px;
    }

    .existing-uploads {
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
        margin-top: 30px;
    }

    .existing-uploads h5 {
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4">Upload Photos</h1>
    <p class="lead mb-4">Upload photos of your pet to create a beautiful memorial video.</p>

    <div class="card mb-5">
        <div class="card-body">
            <h5 class="card-title">Upload Guidelines</h5>
            <div class="alert alert-info mb-3">
                <strong>Important:</strong> Upload <strong>10-25 photos</strong> of your pet for the most accurate AI-generated memorial video. The more photos you provide, the better RunwayML can capture your pet's unique appearance and personality.
            </div>
            <ul class="mb-0">
                <li><strong>Minimum required:</strong> 10 photos to proceed to video creation</li>
                <li><strong>Recommended:</strong> 15-25 photos for best results</li>
                <li>For best results, upload high-quality images</li>
                <li>Maximum file size: 10MB per image</li>
                <li>Supported formats: JPG, JPEG, PNG</li>
                <li>You can upload multiple images at once</li>
            </ul>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="upload-area mb-4" id="uploadArea">
                <form id="uploadForm" action="{{ url_for('upload.upload_files') }}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="text-center py-5">
                        <i class="fas fa-cloud-upload-alt fa-4x text-secondary mb-3"></i>
                        <h3>Drag & Drop Files Here</h3>
                        <p class="text-muted">or</p>
                        <label for="fileInput" class="btn btn-primary">
                            Browse Files
                        </label>
                        <input type="file" id="fileInput" name="files[]" multiple style="display: none;" accept=".jpg,.jpeg,.png">
                        <div id="selectedFilesCount" class="mt-2 text-muted" style="display: none;">
                            <small>0 files selected</small>
                        </div>
                    </div>
                </form>
            </div>

            <div id="uploadProgress" class="d-none">
                <h5>Uploading Files...</h5>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <p class="text-center mt-2" id="uploadStatus">0% complete</p>
            </div>

            <div id="uploadPreview" class="upload-preview-container"></div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Your Uploads</h5>
                    <p id="uploadCount" class="card-text">Loading your uploads...</p>
                    <div id="uploadStats" class="d-none">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Images:</span>
                            <span id="imageCount">0</span>
                        </div>

                    </div>
                    <div class="d-grid gap-2">
                        <button id="uploadButton" class="btn btn-primary" disabled>
                            Upload Files
                        </button>
                        <div id="createVideoSection" class="mt-3" style="display: none;">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> You have enough photos to create a video!
                            </div>
                            <a href="{{ url_for('video.templates') }}" class="btn btn-success btn-block">
                                Proceed to Create Video
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">Next Steps</h5>
                    <p class="card-text">After uploading at least 10 photos:</p>
                    <ol class="mb-0">
                        <li>Click "Proceed to Create Video" button above</li>
                        <li>Select a template</li>
                        <li>Choose which uploads to include</li>
                        <li>Complete payment</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('uploadArea');
    const uploadButton = document.getElementById('uploadButton');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.querySelector('.progress-bar');
    const uploadStatus = document.getElementById('uploadStatus');
    const uploadPreview = document.getElementById('uploadPreview');
    const uploadCount = document.getElementById('uploadCount');
    const uploadStats = document.getElementById('uploadStats');
    const imageCount = document.getElementById('imageCount');

    const selectedFilesCount = document.getElementById('selectedFilesCount');

    // Load existing uploads only if there are any
    loadUserUploads();

    // File input change handler
    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('bg-light');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('bg-light');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('bg-light');

        const dt = e.dataTransfer;
        const files = dt.files;

        handleFiles(files);
    });

    // Upload button click handler
    uploadButton.addEventListener('click', function() {
        if (selectedFiles.length > 0) {
            uploadFiles();
        }
    });

    // Store selected files separately to avoid file input issues
    let selectedFiles = [];

    // Handle file selection
    function handleFileSelect(e) {
        const files = e.target.files;
        addFiles(files);
    }

    // Add files to existing selection (accumulative)
    function addFiles(newFiles) {
        if (newFiles.length === 0) return;

        // Enable upload button
        uploadButton.disabled = false;

        let addedCount = 0;
        let duplicateCount = 0;

        Array.from(newFiles).forEach(file => {
            const isImage = file.type.startsWith('image/');

            if (isImage) {
                // Check for duplicates based on name, size, and last modified date
                const isDuplicate = selectedFiles.some(existingFile =>
                    existingFile.name === file.name &&
                    existingFile.size === file.size &&
                    existingFile.lastModified === file.lastModified
                );

                if (!isDuplicate && selectedFiles.length < 25) {
                    selectedFiles.push(file);
                    addedCount++;
                } else if (isDuplicate) {
                    duplicateCount++;
                }
            }
        });

        // Update the file input with all selected files
        updateFileInput();

        // Show messages
        if (selectedFiles.length >= 25) {
            showMessage('Maximum 25 files allowed. Additional files were not added.', 'warning');
        } else if (duplicateCount > 0) {
            showMessage(`${duplicateCount} duplicate file(s) were skipped.`, 'info');
        }

        if (addedCount > 0) {
            showMessage(`${addedCount} file(s) added successfully.`, 'success');
        }

        // Refresh the preview to show all files
        refreshPreview();

        // Update file counter
        updateFileCounter();
    }

    // Update the file input with selected files
    function updateFileInput() {
        const dataTransfer = new DataTransfer();
        selectedFiles.forEach(file => {
            dataTransfer.items.add(file);
        });
        fileInput.files = dataTransfer.files;
    }

    // Update file counter display
    function updateFileCounter() {
        const count = selectedFiles.length;
        if (count > 0) {
            selectedFilesCount.style.display = 'block';
            selectedFilesCount.innerHTML = `<small>${count} file${count !== 1 ? 's' : ''} selected (max 25)</small>`;
            uploadButton.textContent = `Upload ${count} File${count !== 1 ? 's' : ''}`;
        } else {
            selectedFilesCount.style.display = 'none';
            uploadButton.textContent = 'Upload Files';
        }
    }

    // Show temporary message
    function showMessage(text, type) {
        const alertClass = type === 'warning' ? 'alert-warning' :
                          type === 'info' ? 'alert-info' : 'alert-success';

        const message = document.createElement('div');
        message.className = `alert ${alertClass} mt-2`;
        message.textContent = text;
        uploadPreview.parentNode.insertBefore(message, uploadPreview);

        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    // Process selected files (for drag and drop)
    function handleFiles(files) {
        // For drag and drop, we add to existing files rather than replacing
        addFiles(files);
    }

    // Refresh preview to show all selected files
    function refreshPreview() {
        // Clear current preview
        uploadPreview.innerHTML = '';

        // Create previews for all selected files
        selectedFiles.forEach((file, index) => {
            const isImage = file.type.startsWith('image/');

            if (!isImage) return;

            const previewItem = document.createElement('div');
            previewItem.className = 'upload-preview-item';
            previewItem.dataset.fileIndex = index;

            const img = document.createElement('img');
            img.file = file;
            previewItem.appendChild(img);

            const reader = new FileReader();
            reader.onload = (function(aImg) {
                return function(e) {
                    aImg.src = e.target.result;
                };
            })(img);
            reader.readAsDataURL(file);

            // Add remove button for individual files
            const removeBtn = document.createElement('div');
            removeBtn.className = 'remove-btn';
            removeBtn.innerHTML = '×';
            removeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                removeFileFromSelection(index);
            });
            previewItem.appendChild(removeBtn);

            uploadPreview.appendChild(previewItem);
        });
    }

    // Remove a file from the selection
    function removeFileFromSelection(fileIndex) {
        // Remove the file from our selectedFiles array
        selectedFiles.splice(fileIndex, 1);

        // Update the file input
        updateFileInput();

        // Disable upload button if no files
        if (selectedFiles.length === 0) {
            uploadButton.disabled = true;
        }

        // Refresh the preview
        refreshPreview();

        // Update file counter
        updateFileCounter();
    }

    // Upload files to server
    function uploadFiles() {
        const formData = new FormData(uploadForm);

        // Show progress
        uploadProgress.classList.remove('d-none');
        uploadButton.disabled = true;

        // Clear any existing uploads container before starting new upload
        const existingContainer = document.querySelector('.existing-uploads');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Use XMLHttpRequest to track upload progress
        const xhr = new XMLHttpRequest();
        xhr.open('POST', uploadForm.action, true);

        xhr.upload.onprogress = function(e) {
            if (e.lengthComputable) {
                const percentComplete = Math.round((e.loaded / e.total) * 100);
                progressBar.style.width = percentComplete + '%';
                uploadStatus.textContent = percentComplete + '% complete';
            }
        };

        xhr.onload = function() {
            if (xhr.status === 200) {
                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success mt-3';
                successMessage.textContent = 'Files uploaded successfully!';
                uploadProgress.after(successMessage);

                // Reset the form
                uploadForm.reset();
                uploadPreview.innerHTML = '';
                uploadButton.disabled = true;

                // Clear selected files array
                selectedFiles = [];

                // Update file counter
                updateFileCounter();

                // Hide progress after a delay
                setTimeout(() => {
                    uploadProgress.classList.add('d-none');
                    progressBar.style.width = '0%';
                    uploadStatus.textContent = '0% complete';
                }, 1000);

                // Reload user uploads to show the new files
                setTimeout(() => {
                    loadUserUploads();

                    // Remove success message after 5 seconds
                    setTimeout(() => {
                        successMessage.remove();
                    }, 5000);
                }, 1500);
            } else {
                // Handle error
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger mt-3';
                errorMessage.textContent = 'Upload failed. Please try again.';
                uploadProgress.after(errorMessage);

                uploadButton.disabled = false;
                uploadProgress.classList.add('d-none');

                // Remove error message after 5 seconds
                setTimeout(() => {
                    errorMessage.remove();
                }, 5000);
            }
        };

        xhr.onerror = function() {
            const errorMessage = document.createElement('div');
            errorMessage.className = 'alert alert-danger mt-3';
            errorMessage.textContent = 'Network error occurred. Please try again.';
            uploadProgress.after(errorMessage);

            uploadButton.disabled = false;
            uploadProgress.classList.add('d-none');

            // Remove error message after 5 seconds
            setTimeout(() => {
                errorMessage.remove();
            }, 5000);
        };

        xhr.send(formData);
    }

    // Load user uploads from API
    function loadUserUploads() {
        fetch('/api/uploads', {
            headers: {
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            }
        })
            .then(response => response.json())
            .then(data => {
                // Update upload counts
                uploadCount.textContent = `You have ${data.length} uploaded files`;

                // Count images
                const images = data.filter(item => item.file_type === 'image');

                imageCount.textContent = images.length;

                // Update upload count with progress toward minimum
                if (images.length < 10) {
                    uploadCount.innerHTML = `You have ${images.length} photos uploaded<br><small class="text-warning">Need ${10 - images.length} more photos to create a video</small>`;
                } else {
                    uploadCount.innerHTML = `You have ${images.length} photos uploaded<br><small class="text-success">Ready to create a video!</small>`;
                }

                // Show stats
                uploadStats.classList.remove('d-none');

                // Show/hide create video section based on photo count
                const createVideoSection = document.getElementById('createVideoSection');
                if (images.length >= 10) {
                    createVideoSection.style.display = 'block';
                } else {
                    createVideoSection.style.display = 'none';
                }

                // Display existing uploads
                displayExistingUploads(data);
            })
            .catch(error => {
                console.error('Error loading uploads:', error);
                uploadCount.textContent = 'Error loading your uploads';
            });
    }

    // Display existing uploads
    function displayExistingUploads(uploads) {
        // Remove any existing uploads container first
        const existingContainer = document.querySelector('.existing-uploads');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Only show the section if there are uploads OR if we want to show the "no uploads" message
        // Create a container for existing uploads
        const existingUploadsContainer = document.createElement('div');
        existingUploadsContainer.className = 'existing-uploads mt-4';
        existingUploadsContainer.innerHTML = '<h5>Your Existing Uploads</h5>';

        // Create a grid for the uploads
        const uploadsGrid = document.createElement('div');
        uploadsGrid.className = 'upload-preview-container';

        if (uploads.length === 0) {
            uploadsGrid.innerHTML = '<p class="text-muted">No uploads yet. Use the form above to upload files.</p>';
        } else {
            // Sort uploads by date (newest first)
            uploads.sort((a, b) => new Date(b.uploaded_at) - new Date(a.uploaded_at));

            // Add each upload to the grid
            uploads.forEach(upload => {
                const uploadItem = document.createElement('div');
                uploadItem.className = 'upload-preview-item';
                uploadItem.dataset.id = upload.id;

                const img = document.createElement('img');
                img.src = upload.url;
                img.alt = upload.file_name;
                uploadItem.appendChild(img);

                // Add delete button
                const deleteBtn = document.createElement('div');
                deleteBtn.className = 'remove-btn';
                deleteBtn.innerHTML = '×'; // Use HTML entity for X instead of FontAwesome
                deleteBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (confirm('Are you sure you want to delete this file?')) {
                        deleteUpload(upload.id);
                    }
                });
                uploadItem.appendChild(deleteBtn);

                uploadsGrid.appendChild(uploadItem);
            });
        }

        existingUploadsContainer.appendChild(uploadsGrid);

        // Add to the page after the upload preview
        const uploadPreviewParent = uploadPreview.parentNode;
        uploadPreviewParent.appendChild(existingUploadsContainer);
    }

    // Delete an upload
    function deleteUpload(uploadId) {
        fetch(`/api/uploads/${uploadId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the upload from the UI
                const uploadElement = document.querySelector(`.upload-preview-item[data-id="${uploadId}"]`);
                if (uploadElement) {
                    uploadElement.remove();
                }

                // Reload the uploads to update counts
                loadUserUploads();

                // Show success message
                alert('File deleted successfully');
            } else {
                alert('Failed to delete file: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error deleting upload:', error);
            alert('Failed to delete file. Please try again.');
        });
    }
});
</script>
{% endblock %}
